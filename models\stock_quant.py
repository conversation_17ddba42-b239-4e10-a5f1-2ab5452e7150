from odoo import fields, models, _

class StockQuant(models.Model):
    _inherit = 'stock.quant'

    type1_ids = fields.Many2many(related="product_tmpl_id.type1_ids", string='Produit Type 1', requred=True)
    type2_ids = fields.Many2many(related="product_tmpl_id.type2_ids", string='Produit Type 2', requred=True)
    type3_ids = fields.Many2many(related="product_tmpl_id.type3_ids", string='Produit Type 3', requred=True)
    classification_id = fields.Many2one(
        related="product_tmpl_id.classification_id",
        string='Produit Classification',
        required=True,
        store=True,  # <= Ajout crucial
    )
    sub_category_ids = fields.Many2many(related="product_tmpl_id.sub_category_ids", string='Sous catégories',
                                        requred=True)
    brand_id = fields.Many2one(
        related='product_tmpl_id.feed_brand_id',
        string='Marque produit',
        required=True,
        store=True,
    )
