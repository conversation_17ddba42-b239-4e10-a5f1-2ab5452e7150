<odoo>
    <record id="view_stock_quant_search_not_done" model="ir.ui.view">
        <field name="name">stock.quant.search.not.done</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.quant_search_view"/>
        <field name="arch" type="xml">
            <filter name="to_apply" position="after">

                <!-- Seuls les group_by sur des champs Many2one sont conservés -->
                <filter
                        name="classification_id"
                        string="Classification"
                        context="{'group_by': 'classification_id'}"
                />
                <filter
                        name="brand_id"
                        string="Marque Produit"
                        context="{'group_by': 'brand_id'}"
                />

                <separator/>

                <!-- Filtres basés sur le domaine (affichage conditionnel) -->
                <filter
                        name="filter_type1"
                        string="Produit Type 1"
                        domain="[('type1_ids','!=',False)]"/>
                <filter
                        name="filter_type2"
                        string="Produit Type 2"
                        domain="[('type2_ids','!=',False)]"/>
                <filter
                        name="filter_type3"
                        string="Produit Type 3"
                        domain="[('type3_ids','!=',False)]"/>
                <filter
                        name="filter_classification"
                        string="Produit Classification"
                        domain="[('classification_id','!=',False)]"/>
                <filter
                        name="filter_sub_categories"
                        string='Sous catégories'
                        domain="[('sub_category_ids','!=',False)]"/>
                <filter
                        name="filter_brand"
                        string='Marque Produit'
                        domain="[('brand_id','!=',False)]"/>

                <separator/>

            </filter>
        </field>
    </record>
</odoo>
