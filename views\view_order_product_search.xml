<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="sale_report_search" model="ir.ui.view">
        <field name="model">sale.report</field> <!-- 🔁 Correction ici -->
        <field name="inherit_id" ref="sale.view_order_product_search"/>
        <field name="arch" type="xml">
            <filter name="Customer" position="before">
                <!-- ✅ Groupement par champs computed (Many2many → Char) -->
                <filter
                        name="product_type1_ids"
                        string="Produit Type 1"
                        context="{'group_by': 'product_type1_ids'}"
                />
                <filter
                        name="product_type2_ids"
                        string="Produit Type 2"
                        context="{'group_by': 'product_type2_ids'}"
                />
                <filter
                        name="product_type3_ids"
                        string="Produit Type 3"
                        context="{'group_by': 'product_type3_ids'}"
                />
                <filter
                        name="product_sub_categories"
                        string="Sous Catégories"
                        context="{'group_by': 'product_sub_categories'}"
                />
                <separator/>
                <!-- ✅ Groupement par champs Many2one -->
                <filter
                        name="classification_id"
                        string="Classification Produit"
                        context="{'group_by': 'classification_id'}"
                />
                <filter
                        name="product_brand_id"
                        string="Marque Produit"
                        context="{'group_by': 'brand_id'}"
                />
                <separator/>
                <!-- ✅ Groupement partenaires computed -->
                <filter
                        name="partner_type1_names"
                        string="Client Type 1"
                        context="{'group_by': 'partner_type1_names'}"
                />
                <filter
                        name="partner_type2_names"
                        string="Client Type 2"
                        context="{'group_by': 'partner_type2_names'}"
                />
                <separator/>
                <!-- ✅ Groupement partenaires Many2one -->
                <filter
                        name="partner_classement_id"
                        string="Client Classement"
                        context="{'group_by': 'partner_classement_id'}"
                />
                <filter
                        name="partner_secteur_id"
                        string="Client Secteur"
                        context="{'group_by': 'partner_secteur_id'}"
                />
                <separator/>
                <filter
                        name="filter_type1"
                        string="Produit Type 1"
                        domain="[('type1_ids','!=',False)]"/>
                <filter
                        name="filter_type2"
                        string="Produit Type 2"
                        domain="[('type2_ids','!=',False)]"/>
                <filter
                        name="filter_type3"
                        string="Produit Type 3"
                        domain="[('type3_ids','!=',False)]"/>
                <filter
                        name="classification_id"
                        string="Produit Classification"
                        domain="[('classification_id','!=',False)]"/>
                <filter
                        name="sub_category_ids"
                        string='Sous catégories'
                        domain="[('sub_category_ids','!=',False)]"/>
                <filter
                        name="brand_id"
                        string='Marque Produit'
                        domain="[('brand_id','!=',False)]"/>
                <separator/>
                <filter
                        name="partner_type1_ids"
                        string="Client Type 1"
                        domain="[('partner_type1_ids','!=',False)]"/>
                <filter
                        name="partner_type2_ids"
                        string="Client Type 2"
                        domain="[('partner_type2_ids','!=',False)]"/>
                <filter
                        name="partner_classement_id"
                        string="Client Classement"
                        domain="[('partner_classement_id','!=',False)]"/>
                <filter
                        name="partner_secteur_id"
                        string="Client Secteur"
                        domain="[('partner_secteur_id','!=',False)]"/>
            </filter>
        </field>
    </record>
</odoo>
