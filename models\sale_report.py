
from odoo import fields, models, api


class SaleReport(models.Model):
    _inherit = 'sale.report'

    type1_ids = fields.Many2many(related="product_id.product_tmpl_id.type1_ids", string='Produit Type 1')
    product_type1_ids = fields.Char(string='Produit Type 1', compute='_get_tags', store=True)
    type2_ids = fields.Many2many(related="product_id.product_tmpl_id.type2_ids", string='Produit Type 2')
    type3_ids = fields.Many2many(related="product_id.product_tmpl_id.type3_ids", string='Produit Type 3')
    classification_id = fields.Many2one(related="product_id.product_tmpl_id.classification_id",
                                        string='Produit Classification', required=True)
    sub_category_ids = fields.Many2many(related="product_id.product_tmpl_id.sub_category_ids", required='Sous catégories')
    brand_id = fields.Many2one(related='product_id.product_tmpl_id.feed_brand_id', string='Marque produit', required=True)

    partner_type1_ids = fields.Many2many(related="partner_id.type1_ids", string="Client Type 1")
    partner_type2_ids = fields.Many2many(related="partner_id.type2_ids", string="Client Type 2")
    partner_classement_id = fields.Many2one(related="partner_id.classement_id", string="Client Classement", required=True)
    partner_secteur_id = fields.Many2one(related="partner_id.secteur_id", string="Client Secteur", required=True)

    @api.model
    @api.depends('type1_ids', 'product_id.product_tmpl_id.type1_ids')
    def _get_tags(self):
        for rec in self:
            if rec.type1_ids:
                product_type1_ids = ','.join([p.name for p in rec.type1_ids])
            else:
                product_type1_ids = ''
            rec.product_type1_ids = product_type1_ids