
from odoo import fields, models, api


class SaleReport(models.Model):
    _inherit = 'sale.report'

    # Champs Many2many originaux
    type1_ids = fields.Many2many(related="product_id.product_tmpl_id.type1_ids", string='Produit Type 1')
    type2_ids = fields.Many2many(related="product_id.product_tmpl_id.type2_ids", string='Produit Type 2')
    type3_ids = fields.Many2many(related="product_id.product_tmpl_id.type3_ids", string='Produit Type 3')
    sub_category_ids = fields.Many2many(related="product_id.product_tmpl_id.sub_category_ids", string='Sous catégories')

    # Champs Many2one (groupables directement)
    classification_id = fields.Many2one(related="product_id.product_tmpl_id.classification_id",
                                        string='Produit Classification', store=True)
    brand_id = fields.Many2one(related='product_id.product_tmpl_id.feed_brand_id', string='Marque produit', store=True)

    # Champs computed pour groupement (Many2many → Char)
    product_type1_ids = fields.Char(string='Produit Type 1 (Groupable)', compute='_compute_groupable_fields', store=True)
    product_type2_ids = fields.Char(string='Produit Type 2 (Groupable)', compute='_compute_groupable_fields', store=True)
    product_type3_ids = fields.Char(string='Produit Type 3 (Groupable)', compute='_compute_groupable_fields', store=True)
    product_sub_categories = fields.Char(string='Sous Catégories (Groupable)', compute='_compute_groupable_fields', store=True)

    # Champs partenaires Many2many
    partner_type1_ids = fields.Many2many(related="partner_id.type1_ids", string="Client Type 1")
    partner_type2_ids = fields.Many2many(related="partner_id.type2_ids", string="Client Type 2")

    # Champs partenaires Many2one (groupables directement)
    partner_classement_id = fields.Many2one(related="partner_id.classement_id", string="Client Classement", store=True)
    partner_secteur_id = fields.Many2one(related="partner_id.secteur_id", string="Client Secteur", store=True)

    # Champs partenaires computed pour groupement
    partner_type1_names = fields.Char(string='Client Type 1 (Groupable)', compute='_compute_groupable_fields', store=True)
    partner_type2_names = fields.Char(string='Client Type 2 (Groupable)', compute='_compute_groupable_fields', store=True)

    @api.depends('type1_ids', 'type2_ids', 'type3_ids', 'sub_category_ids',
                 'partner_type1_ids', 'partner_type2_ids')
    def _compute_groupable_fields(self):
        """Convertit les champs Many2many en chaînes pour permettre le groupement"""
        for rec in self:
            # Champs produits
            rec.product_type1_ids = ', '.join(rec.type1_ids.mapped('name')) if rec.type1_ids else ''
            rec.product_type2_ids = ', '.join(rec.type2_ids.mapped('name')) if rec.type2_ids else ''
            rec.product_type3_ids = ', '.join(rec.type3_ids.mapped('name')) if rec.type3_ids else ''
            rec.product_sub_categories = ', '.join(rec.sub_category_ids.mapped('name')) if rec.sub_category_ids else ''

            # Champs partenaires
            rec.partner_type1_names = ', '.join(rec.partner_type1_ids.mapped('name')) if rec.partner_type1_ids else ''
            rec.partner_type2_names = ', '.join(rec.partner_type2_ids.mapped('name')) if rec.partner_type2_ids else ''