from odoo import fields, models

class ReportProjectTaskUser(models.Model):
    _inherit = 'report.project.task.user'

    partner_type1_ids = fields.Many2many(related="partner_id.type1_ids", string="Client Type 1")
    partner_type2_ids = fields.Many2many(related="partner_id.type2_ids", string="Client Type 2")
    partner_classement_id = fields.Many2one(related="partner_id.classement_id", string="Client Classement")
    partner_secteur_id = fields.Many2one(related="partner_id.secteur_id", string="Client Secteur")