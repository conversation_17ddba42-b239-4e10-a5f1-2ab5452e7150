<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    stock.view_move_search
    <record id="view_move_search" model="ir.ui.view">
        <field name="name">stock.move.search</field>
        <field name="model">stock.move</field>
        <field name="inherit_id" ref="stock.view_move_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <filter
                        name="filter_type1"
                        string="Produit Type 1"
                        domain="[('type1_ids','!=',False)]"/>
                <filter
                        name="filter_type2"
                        string="Produit Type 2"
                        domain="[('type2_ids','!=',False)]"/>
                <filter
                        name="filter_type3"
                        string="Produit Type 3"
                        domain="[('type3_ids','!=',False)]"/>
                <filter
                        name="classification_id"
                        string="Produit Classification"
                        domain="[('classification_id','!=',False)]"/>
                <filter
                        name="sub_category_ids"
                        string='Sous catégories'
                        domain="[('sub_category_ids','!=',False)]"/>
                <filter
                        name="brand_id"
                        string='Marque Produit'
                        domain="[('brand_id','!=',False)]"/>
                <separator/>
                <filter
                        name="partner_type1_ids"
                        string="Client Type 1"
                        domain="[('partner_type1_ids','!=',False)]"/>
                <filter
                        name="partner_type2_ids"
                        string="Client Type 2"
                        domain="[('partner_type2_ids','!=',False)]"/>
                <filter
                        name="partner_classement_id"
                        string="Client Classement"
                        domain="[('partner_classement_id','!=',False)]"/>
                <filter
                        name="partner_secteur_id"
                        string="Client Secteur"
                        domain="[('partner_secteur_id','!=',False)]"/>
            </xpath>
        </field>
    </record>
</odoo>