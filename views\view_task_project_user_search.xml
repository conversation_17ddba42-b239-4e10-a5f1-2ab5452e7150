<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_task_project_user_search" model="ir.ui.view">
        <field name="model">report.project.task.user</field>
        <field name="inherit_id" ref="project.view_task_project_user_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter" position="inside">
                <separator/>
                <!-- GROUP BY (seulement champs stockés et groupables) -->
                <filter name="partner_type1_ids" string="Client Type 1"
                        context="{'group_by': 'partner_type1_ids'}"/>
                <filter name="partner_type2_ids" string="Client Type 2"
                        context="{'group_by': 'partner_type2_ids'}"/>
                <filter name="partner_classement_id" context="{'group_by': 'partner_classement_id'}"/>
                <filter name="partner_secteur_id" context="{'group_by': 'partner_secteur_id'}"/>

                <separator/>

                <!-- Filtres simples (pas de group_by) -->
                <filter name="partner_type1_ids" string="Client Type 1" domain="[('partner_type1_ids','!=',False)]"/>
                <filter name="partner_type2_ids" string="Client Type 2" domain="[('partner_type2_ids','!=',False)]"/>
                <filter name="partner_classement_id" string="Client Classement"
                        domain="[('partner_classement_id','!=',False)]"/>
                <filter name="partner_secteur_id" string="Client Secteur" domain="[('partner_secteur_id','!=',False)]"/>

                <separator/>
            </xpath>
        </field>
    </record>
</odoo>
