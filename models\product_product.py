from odoo import api, fields, models, _

class ProductProduct(models.Model):
    _inherit = 'product.product'

    type1_ids = fields.Many2many(related="product_tmpl_id.type1_ids", string='Produit Type 1', requred=True)
    type2_ids = fields.Many2many(related="product_tmpl_id.type2_ids", string='Produit Type 2', requred=True)
    type3_ids = fields.Many2many(related="product_tmpl_id.type3_ids", string='Produit Type 3', requred=True)
    classification_id = fields.Many2one(related="product_tmpl_id.classification_id", string='Produit Classification',
                                        requred=True)
    sub_category_ids = fields.Many2many(related="product_tmpl_id.sub_category_ids", string='Sous catégories',
                                        requred=True)
    brand_id = fields.Many2one(related='product_tmpl_id.feed_brand_id', string='Marque produit', requred=True)

    partner_type1_ids = fields.Many2many(related="partner_id.type1_ids", string="Client Type 1", requred=True)
    partner_type2_ids = fields.Many2many(related="partner_id.type2_ids", string="Client Type 2", requred=True)
    partner_classement_id = fields.Many2one(related="partner_id.classement_id", string="Client Classement", requred=True)
    partner_secteur_id = fields.Many2one(related="partner_id.secteur_id", string="Client Secteur", requred=True)
