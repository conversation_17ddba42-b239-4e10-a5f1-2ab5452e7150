<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--    <record id="stock_location_route_view_form_inherit_stock_delivery" model="ir.ui.view">-->
    <!--        <field name="name">stock.route.form</field>-->
    <!--        <field name="model">stock.route</field>-->
    <!--        <field name="inherit_id" ref="stock.stock_location_route_form_view"/>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <xpath expr="//group[@name='route_selector']/group" position="inside">-->
    <!--                <field name="shipping_selectable" string="Shipping Methods"/>-->
    <!--            </xpath>-->
    <!--        </field>-->
    <!--    </record>-->

    <record model="ir.ui.view" id="stock_move_line_view_search_delivery">
        <field name="name">stock.move.line.search.delivery</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.stock_move_line_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='groupby']" position="inside">
                <!-- ✅ Groupement uniquement par champs Many2one (pas Many2many) -->
                <filter
                        name="classification_id"
                        string="Classification Produit"
                        context="{'group_by': 'classification_id'}"
                />
                <filter
                        name="product_brand_id"
                        string="Marque Produit"
                        context="{'group_by': 'brand_id'}"
                />
                <separator/>
<!--                <filter-->
<!--                        name="partner_type1_ids"-->
<!--                        context="{'group_by': 'partner_type1_ids'}"-->
<!--                />-->
<!--                <filter-->
<!--                        name="partner_type2_ids"-->
<!--                        context="{'group_by': 'partner_type2_ids'}"-->
<!--                />-->
<!--                <filter-->
<!--                        name="partner_classement_id"-->
<!--                        context="{'group_by': 'partner_classement_id'}"-->
<!--                />-->
<!--                <filter-->
<!--                        name="partner_secteur_id"-->
<!--                        context="{'group_by': 'partner_secteur_id'}"-->
<!--                />-->
                <separator/>
                <filter
                        name="filter_type1"
                        string="Produit Type 1"
                        domain="[('type1_ids','!=',False)]"/>
                <filter
                        name="filter_type2"
                        string="Produit Type 2"
                        domain="[('type2_ids','!=',False)]"/>
                <filter
                        name="filter_type3"
                        string="Produit Type 3"
                        domain="[('type3_ids','!=',False)]"/>
                <filter
                        name="classification_id"
                        string="Produit Classification"
                        domain="[('classification_id','!=',False)]"/>
                <filter
                        name="sub_category_ids"
                        string='Sous catégories'
                        domain="[('sub_category_ids','!=',False)]"/>
                <filter
                        name="brand_id"
                        string='Marque Produit'
                        domain="[('brand_id','!=',False)]"/>
                <separator/>
<!--                <filter-->
<!--                        name="partner_type1_ids"-->
<!--                        string="Client Type 1"-->
<!--                        domain="[('partner_type1_ids','!=',False)]"/>-->
<!--                <filter-->
<!--                        name="partner_type2_ids"-->
<!--                        string="Client Type 2"-->
<!--                        domain="[('partner_type2_ids','!=',False)]"/>-->
<!--                <filter-->
<!--                        name="partner_classement_id"-->
<!--                        string="Client Classement"-->
<!--                        domain="[('partner_classement_id','!=',False)]"/>-->
<!--                <filter-->
<!--                        name="partner_secteur_id"-->
<!--                        string="Client Secteur"-->
<!--                        domain="[('partner_secteur_id','!=',False)]"/>-->
            </xpath>
        </field>
    </record>
</odoo>
