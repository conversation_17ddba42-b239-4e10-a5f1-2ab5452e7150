# -*- coding: utf-8 -*-
from odoo import fields, models, api

class Lead(models.Model):
    _inherit = 'crm.lead'

    # Champs Many2many related (non groupables directement)
    partner_type1_ids = fields.Many2many(related="partner_id.type1_ids", string="Client Type 1", readonly=True)
    partner_type2_ids = fields.Many2many(related="partner_id.type2_ids", string="Client Type 2", readonly=True)

    # Champs Many2one related (ajouter store=True pour le group_by)
    partner_classement_id = fields.Many2one(related="partner_id.classement_id", string="Client Classement", store=True, readonly=True)
    partner_secteur_id = fields.Many2one(related="partner_id.secteur_id", string="Client Secteur", store=True, readonly=True)

    # Champs Many2one calculés pour permettre group_by (première valeur des M2M)
    partner_type1_first_id = fields.Many2one(
        'res.partner.type1',
        string="Premier Type 1",
        compute='_compute_partner_type1_first_id',
        store=True
    )

    partner_type2_first_id = fields.Many2one(
        'res.partner.type2',
        string="Premier Type 2",
        compute='_compute_partner_type2_first_id',
        store=True
    )

    @api.depends('partner_type1_ids')
    def _compute_partner_type1_first_id(self):
        for lead in self:
            lead.partner_type1_first_id = lead.partner_type1_ids[:1].id if lead.partner_type1_ids else False

    @api.depends('partner_type2_ids')
    def _compute_partner_type2_first_id(self):
        for lead in self:
            lead.partner_type2_first_id = lead.partner_type2_ids[:1].id if lead.partner_type2_ids else False
