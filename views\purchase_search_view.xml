<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_purchase_order_search" model="ir.ui.view">
        <field name="name">
            purchase.report.view.search.inherit.product.brand.purchase
        </field>
        <field name="model">purchase.report</field>
        <field name="inherit_id" ref="purchase.view_purchase_order_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='user_id']" position="after">
                <separator/>
                <filter
                        name="type1_ids"
                        context="{'group_by': 'type1_ids'}"
                />
                <filter
                        name="type2_ids"
                        context="{'group_by': 'type2_ids'}"
                />
                <filter
                        name="type3_ids"
                        context="{'group_by': 'type3_ids'}"
                />
                <filter
                        name="classification_id"
                        context="{'group_by': 'classification_id'}"
                />
                <filter
                        name="sub_category_ids"
                        context="{'group_by': 'sub_category_ids'}"
                />
                <filter
                        name="product_brand_id"
                        context="{'group_by': 'brand_id'}"
                />
                <separator/>
                <filter
                        name="partner_type1_ids"
                        context="{'group_by': 'partner_type1_ids'}"
                />
                <filter
                        name="partner_type2_ids"
                        context="{'group_by': 'partner_type2_ids'}"
                />
                <filter
                        name="partner_classement_id"
                        context="{'group_by': 'partner_classement_id'}"
                />
                <filter
                        name="partner_secteur_id"
                        context="{'group_by': 'partner_secteur_id'}"
                />
                <separator/>
                <filter
                        name="filter_type1"
                        string="Produit Type 1"
                        domain="[('type1_ids','!=',False)]"/>
                <filter
                        name="filter_type2"
                        string="Produit Type 2"
                        domain="[('type2_ids','!=',False)]"/>
                <filter
                        name="filter_type3"
                        string="Produit Type 3"
                        domain="[('type3_ids','!=',False)]"/>
                <filter
                        name="classification_id"
                        string="Produit Classification"
                        domain="[('classification_id','!=',False)]"/>
                <filter
                        name="sub_category_ids"
                        string='Sous catégories'
                        domain="[('sub_category_ids','!=',False)]"/>
                <filter
                        name="brand_id"
                        string='Marque Produit'
                        domain="[('brand_id','!=',False)]"/>
                <separator/>
                <filter
                        name="partner_type1_ids"
                        string="Client Type 1"
                        domain="[('partner_type1_ids','!=',False)]"/>
                <filter
                        name="partner_type2_ids"
                        string="Client Type 2"
                        domain="[('partner_type2_ids','!=',False)]"/>
                <filter
                        name="partner_classement_id"
                        string="Client Classement"
                        domain="[('partner_classement_id','!=',False)]"/>
                <filter
                        name="partner_secteur_id"
                        string="Client Secteur"
                        domain="[('partner_secteur_id','!=',False)]"/>

                <separator/>
            </xpath>
        </field>
    </record>
</odoo>
