<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="crm_lead_report_search" model="ir.ui.view">
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.view_crm_case_opportunities_filter"/>
        <field name="arch" type="xml">
            <filter name="message_needaction" position="before">

                <!-- GROUP BY (seulement champs stockés et groupables) -->
                <filter name="partner_type1_ids" string="Client Type 1" context="{'group_by': 'partner_type1_ids'}"/>
                <filter name="partner_type2_ids" string="Client Type 2" context="{'group_by': 'partner_type2_ids'}"/>
                <filter name="partner_classement_id" context="{'group_by': 'partner_classement_id'}"/>
                <filter name="partner_secteur_id" context="{'group_by': 'partner_secteur_id'}"/>

                <separator/>

                <!-- Filtres simples (pas de group_by) -->
                <filter name="partner_type1_ids" string="Client Type 1" domain="[('partner_type1_ids','!=',False)]"/>
                <filter name="partner_type2_ids" string="Client Type 2" domain="[('partner_type2_ids','!=',False)]"/>
                <filter name="partner_classement_id" string="Client Classement" domain="[('partner_classement_id','!=',False)]"/>
                <filter name="partner_secteur_id" string="Client Secteur" domain="[('partner_secteur_id','!=',False)]"/>

                <separator/>
            </filter>
        </field>
    </record>
</odoo>
