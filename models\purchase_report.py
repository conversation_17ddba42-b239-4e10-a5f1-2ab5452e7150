# -*- coding: utf-8 -*-

from odoo import fields, models
class PurchaseReport(models.Model):
    _inherit = "purchase.report"

    partner_type1_ids = fields.Many2many(related="partner_id.type1_ids", string="Client Type 1")
    partner_type2_ids = fields.Many2many(related="partner_id.type2_ids", string="Client Type 2")
    partner_classement_id = fields.Many2one(related="partner_id.classement_id", string="Client Classement")
    partner_secteur_id = fields.Many2one(related="partner_id.secteur_id", string="Client Secteur")

    type1_ids = fields.Many2many(related="product_tmpl_id.type1_ids", string='Produit Type 1')
    type2_ids = fields.Many2many(related="product_tmpl_id.type2_ids", string='Produit Type 2')
    type3_ids = fields.Many2many(related="product_tmpl_id.type3_ids", string='Produit Type 3')
    classification_id = fields.Many2one(related="product_tmpl_id.classification_id", string='Produit Classification')
    sub_category_ids = fields.Many2many(related="product_tmpl_id.sub_category_ids", string='Sous catégories')
    brand_id = fields.Many2one(related='product_tmpl_id.feed_brand_id', string='Marque produit')