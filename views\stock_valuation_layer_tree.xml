<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    stock.view_move_search
    <record id="view_inventory_valuation_search" model="ir.ui.view">
        <field name="name">stock.move.search</field>
        <field name="model">stock.valuation.layer</field>
        <field name="inherit_id" ref="stock.view_inventory_valuation_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='has_remaining_qty']" position="before">
                <filter
                        name="filter_type1"
                        string="Produit Type 1"
                        domain="[('type1_ids','!=',False)]"/>
                <filter
                        name="filter_type2"
                        string="Produit Type 2"
                        domain="[('type2_ids','!=',False)]"/>
                <filter
                        name="filter_type3"
                        string="Produit Type 3"
                        domain="[('type3_ids','!=',False)]"/>
                <filter
                        name="classification_id"
                        string="Produit Classification"
                        domain="[('classification_id','!=',False)]"/>
                <filter
                        name="sub_category_ids"
                        string='Sous catégories'
                        domain="[('sub_category_ids','!=',False)]"/>
                <filter
                        name="brand_id"
                        string='Marque Produit'
                        domain="[('brand_id','!=',False)]"/>
                <separator/>
            </xpath>
        </field>
    </record>
</odoo>